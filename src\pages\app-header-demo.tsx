import { useState } from 'react';
import DynamicAppHeader from '../components/layout/DynamicAppHeader';
import { Card, Text, Heading } from '../components/ui';
import { LightningIcon, BellIcon } from '../components/icons';

// Sample icons for the demo
const SalesIcon = () => (
  <LightningIcon className="w-8 h-8 text-blue-500" />
);

const MessageIcon = () => (
  <svg
    className="w-5 h-5"
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
    />
  </svg>
);

const UserAvatar = () => (
  <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
    JD
  </div>
);

const ListIcon = () => (
  <svg
    className="w-5 h-5"
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M4 6h16M4 10h16M4 14h16M4 18h16"
    />
  </svg>
);

const GridIcon = () => (
  <svg
    className="w-5 h-5"
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"
    />
  </svg>
);

const BarChartIcon = () => (
  <svg
    className="w-5 h-5"
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
    />
  </svg>
);

export default function AppHeaderDemoPage() {
  const [activeNavLink, setActiveNavLink] = useState('Quotations');
  const [activeViewMode, setActiveViewMode] = useState('List');
  const [searchFilters, setSearchFilters] = useState([
    { id: 'undelivered', label: 'Undelivered' },
    { id: 'complete', label: 'Complete' },
  ]);
  const [currentPage, setCurrentPage] = useState(1);
  const [logs, setLogs] = useState<string[]>([]);

  const addLog = (message: string) => {
    setLogs(prev => [
      `${new Date().toLocaleTimeString()}: ${message}`,
      ...prev.slice(0, 9),
    ]);
  };

  const appData = {
    name: 'Sales',
    icon: <SalesIcon />,
    navLinks: [
      {
        label: 'Orders',
        href: '/orders',
        isActive: activeNavLink === 'Orders',
      },
      {
        label: 'Products',
        href: '/products',
        isActive: activeNavLink === 'Products',
      },
      {
        label: 'Quotations',
        href: '/quotations',
        isActive: activeNavLink === 'Quotations',
      },
      {
        label: 'Invoices',
        href: '/invoices',
        isActive: activeNavLink === 'Invoices',
      },
      {
        label: 'Reporting',
        href: '/reporting',
        isActive: activeNavLink === 'Reporting',
      },
      {
        label: 'Configuration',
        href: '/configuration',
        isActive: activeNavLink === 'Configuration',
      },
    ],
  };

  const userData = {
    name: 'John Doe',
    avatar: <UserAvatar />,
    notifications: [
      { count: 3, icon: <BellIcon /> },
      { count: 7, icon: <MessageIcon /> },
    ],
  };

  const viewData = {
    title: 'Quotations',
    actions: [
      {
        label: 'New',
        onClick: () => addLog('New quotation button clicked'),
        isPrimary: true,
      },
      {
        label: 'Upload',
        onClick: () => addLog('Upload button clicked'),
        isPrimary: false,
      },
    ],
    search: {
      filters: searchFilters,
      onSearch: (query: string) => addLog(`Search performed: "${query}"`),
      onRemoveFilter: (id: any) => {
        setSearchFilters(prev => prev.filter(f => f.id !== id));
        addLog(`Filter removed: ${id}`);
      },
    },
    pagination: {
      currentRange: `${(currentPage - 1) * 80 + 1}-${Math.min(currentPage * 80, 150)} / 150`,
      onNext: () => {
        if (currentPage * 80 < 150) {
          setCurrentPage(prev => prev + 1);
          addLog('Next page clicked');
        }
      },
      onPrev: () => {
        if (currentPage > 1) {
          setCurrentPage(prev => prev - 1);
          addLog('Previous page clicked');
        }
      },
    },
    viewModes: [
      { name: 'List', icon: <ListIcon /> },
      { name: 'Kanban', icon: <GridIcon /> },
      { name: 'Graph', icon: <BarChartIcon /> },
    ],
    activeViewMode,
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Dynamic App Header */}
      <DynamicAppHeader
        app={appData}
        user={userData}
        view={viewData}
        data-testid="demo-app-header"
      />

      {/* Demo Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-8">
          {/* Header */}
          <div>
            <Heading level={1} className="mb-4">
              Dynamic App Header Demo
            </Heading>
            <Text variant="subtitle1" color="secondary">
              This page demonstrates the DynamicAppHeader component with
              interactive features. Try clicking buttons, searching, changing
              view modes, and resizing the window to see responsive behavior.
            </Text>
          </div>

          {/* Interactive Controls */}
          <Card variant="elevated" padding="lg">
            <Heading level={3} className="mb-4">
              Interactive Controls
            </Heading>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <Text variant="subtitle2" className="mb-2 font-medium">
                  Active Navigation Link
                </Text>
                <div className="space-y-2">
                  {appData.navLinks.map(link => (
                    <button
                      key={link.label}
                      onClick={() => {
                        setActiveNavLink(link.label);
                        addLog(`Navigation changed to: ${link.label}`);
                      }}
                      className={`block w-full text-left px-3 py-2 rounded-md text-sm transition-colors ${
                        link.isActive
                          ? 'bg-primary text-primary-foreground'
                          : 'hover:bg-muted'
                      }`}
                    >
                      {link.label}
                    </button>
                  ))}
                </div>
              </div>

              <div>
                <Text variant="subtitle2" className="mb-2 font-medium">
                  Active View Mode
                </Text>
                <div className="space-y-2">
                  {viewData.viewModes.map(mode => (
                    <button
                      key={mode.name}
                      onClick={() => {
                        setActiveViewMode(mode.name);
                        addLog(`View mode changed to: ${mode.name}`);
                      }}
                      className={`block w-full text-left px-3 py-2 rounded-md text-sm transition-colors ${
                        mode.name === activeViewMode
                          ? 'bg-primary text-primary-foreground'
                          : 'hover:bg-muted'
                      }`}
                    >
                      {mode.name}
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </Card>

          {/* Activity Log */}
          <Card variant="outlined" padding="lg">
            <Heading level={3} className="mb-4">
              Activity Log
            </Heading>
            <div className="space-y-2 max-h-64 overflow-y-auto">
              {logs.length === 0 ? (
                <Text color="secondary">
                  No activity yet. Try interacting with the header components
                  above.
                </Text>
              ) : (
                logs.map((log, index) => (
                  <div
                    key={index}
                    className="text-sm font-mono p-2 rounded bg-muted"
                  >
                    {log}
                  </div>
                ))
              )}
            </div>
          </Card>
        </div>
      </main>
    </div>
  );
}
