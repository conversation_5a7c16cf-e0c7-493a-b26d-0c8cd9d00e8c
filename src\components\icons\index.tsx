﻿// Professional Icons using React Icons
// Single source for all icons using react-icons library
import React from 'react';
import {
  BsSun,
  BsMoon,
  BsDisplay,
  BsBuilding,
  BsPerson,
  BsGear,
  BsBoxArrowRight,
  BsBook,
  BsKeyboard,
  BsDownload,
  BsPlay,
  BsChevronDown,
  BsChevronLeft,
  BsChevronRight,
  BsCheck,
  BsBell,
  BsArrowLeftRight,
  BsList,
  BsSearch,
  BsGrid,
  BsBarChart,
  BsThreeDotsVertical,
  BsPlus,
  BsUpload,
  BsX,
  BsLightning,
} from 'react-icons/bs';
import { AiOutlineLoading3Quarters } from 'react-icons/ai';

export interface IconProps {
  className?: string;
  size?: number;
  'aria-hidden'?: boolean;
}

// Individual icon components with consistent API
export const SunIcon: React.FC<IconProps> = ({ className = 'w-5 h-5', size, 'aria-hidden': ariaHidden = true }) => {
  const props: any = { className };
  if (size) props.size = size;
  if (ariaHidden !== undefined) props['aria-hidden'] = ariaHidden;
  return <BsSun {...props} />;
};

export const MoonIcon: React.FC<IconProps> = ({ className = 'w-5 h-5', size, 'aria-hidden': ariaHidden = true }) => {
  const props: any = { className };
  if (size) props.size = size;
  if (ariaHidden !== undefined) props['aria-hidden'] = ariaHidden;
  return <BsMoon {...props} />;
};

export const SystemIcon: React.FC<IconProps> = ({ className = 'w-5 h-5', size, 'aria-hidden': ariaHidden = true }) => {
  const props: any = { className };
  if (size) props.size = size;
  if (ariaHidden !== undefined) props['aria-hidden'] = ariaHidden;
  return <BsDisplay {...props} />;
};

export const BuildingIcon: React.FC<IconProps> = ({ className = 'w-5 h-5', size, 'aria-hidden': ariaHidden = true }) => {
  const props: any = { className };
  if (size) props.size = size;
  if (ariaHidden !== undefined) props['aria-hidden'] = ariaHidden;
  return <BsBuilding {...props} />;
};

export const UserIcon: React.FC<IconProps> = ({ className = 'w-5 h-5', size, 'aria-hidden': ariaHidden = true }) => {
  const props: any = { className };
  if (size) props.size = size;
  if (ariaHidden !== undefined) props['aria-hidden'] = ariaHidden;
  return <BsPerson {...props} />;
};

export const SettingsIcon: React.FC<IconProps> = ({ className = 'w-5 h-5', size, 'aria-hidden': ariaHidden = true }) => {
  const props: any = { className };
  if (size) props.size = size;
  if (ariaHidden !== undefined) props['aria-hidden'] = ariaHidden;
  return <BsGear {...props} />;
};

export const LogOutIcon: React.FC<IconProps> = ({ className = 'w-5 h-5', size, 'aria-hidden': ariaHidden = true }) => {
  const props: any = { className };
  if (size) props.size = size;
  if (ariaHidden !== undefined) props['aria-hidden'] = ariaHidden;
  return <BsBoxArrowRight {...props} />;
};

export const BookIcon: React.FC<IconProps> = ({ className = 'w-5 h-5', size, 'aria-hidden': ariaHidden = true }) => {
  const props: any = { className };
  if (size) props.size = size;
  if (ariaHidden !== undefined) props['aria-hidden'] = ariaHidden;
  return <BsBook {...props} />;
};

export const KeyboardIcon: React.FC<IconProps> = ({ className = 'w-5 h-5', size, 'aria-hidden': ariaHidden = true }) => {
  const props: any = { className };
  if (size) props.size = size;
  if (ariaHidden !== undefined) props['aria-hidden'] = ariaHidden;
  return <BsKeyboard {...props} />;
};

export const DownloadIcon: React.FC<IconProps> = ({ className = 'w-5 h-5', size, 'aria-hidden': ariaHidden = true }) => {
  const props: any = { className };
  if (size) props.size = size;
  if (ariaHidden !== undefined) props['aria-hidden'] = ariaHidden;
  return <BsDownload {...props} />;
};

export const PlayIcon: React.FC<IconProps> = ({ className = 'w-5 h-5', size, 'aria-hidden': ariaHidden = true }) => {
  const props: any = { className };
  if (size) props.size = size;
  if (ariaHidden !== undefined) props['aria-hidden'] = ariaHidden;
  return <BsPlay {...props} />;
};

export const ChevronDownIcon: React.FC<IconProps> = ({ className = 'w-5 h-5', size, 'aria-hidden': ariaHidden = true }) => {
  const props: any = { className };
  if (size) props.size = size;
  if (ariaHidden !== undefined) props['aria-hidden'] = ariaHidden;
  return <BsChevronDown {...props} />;
};

export const CheckIcon: React.FC<IconProps> = ({ className = 'w-5 h-5', size, 'aria-hidden': ariaHidden = true }) => {
  const props: any = { className };
  if (size) props.size = size;
  if (ariaHidden !== undefined) props['aria-hidden'] = ariaHidden;
  return <BsCheck {...props} />;
};

export const BellIcon: React.FC<IconProps> = ({ className = 'w-5 h-5', size, 'aria-hidden': ariaHidden = true }) => {
  const props: any = { className };
  if (size) props.size = size;
  if (ariaHidden !== undefined) props['aria-hidden'] = ariaHidden;
  return <BsBell {...props} />;
};

export const SwitchIcon: React.FC<IconProps> = ({ className = 'w-5 h-5', size, 'aria-hidden': ariaHidden = true }) => {
  const props: any = { className };
  if (size) props.size = size;
  if (ariaHidden !== undefined) props['aria-hidden'] = ariaHidden;
  return <BsArrowLeftRight {...props} />;
};

export const MenuIcon: React.FC<IconProps> = ({ className = 'w-5 h-5', size, 'aria-hidden': ariaHidden = true }) => {
  const props: any = { className };
  if (size) props.size = size;
  if (ariaHidden !== undefined) props['aria-hidden'] = ariaHidden;
  return <BsList {...props} />;
};

export const SearchIcon: React.FC<IconProps> = ({ className = 'w-5 h-5', size, 'aria-hidden': ariaHidden = true }) => {
  const props: any = { className };
  if (size) props.size = size;
  if (ariaHidden !== undefined) props['aria-hidden'] = ariaHidden;
  return <BsSearch {...props} />;
};

export const ChevronLeftIcon: React.FC<IconProps> = ({ className = 'w-5 h-5', size, 'aria-hidden': ariaHidden = true }) => {
  const props: any = { className };
  if (size) props.size = size;
  if (ariaHidden !== undefined) props['aria-hidden'] = ariaHidden;
  return <BsChevronLeft {...props} />;
};

export const ChevronRightIcon: React.FC<IconProps> = ({ className = 'w-5 h-5', size, 'aria-hidden': ariaHidden = true }) => {
  const props: any = { className };
  if (size) props.size = size;
  if (ariaHidden !== undefined) props['aria-hidden'] = ariaHidden;
  return <BsChevronRight {...props} />;
};

export const ListIcon: React.FC<IconProps> = ({ className = 'w-5 h-5', size, 'aria-hidden': ariaHidden = true }) => {
  const props: any = { className };
  if (size) props.size = size;
  if (ariaHidden !== undefined) props['aria-hidden'] = ariaHidden;
  return <BsList {...props} />;
};

export const GridIcon: React.FC<IconProps> = ({ className = 'w-5 h-5', size, 'aria-hidden': ariaHidden = true }) => {
  const props: any = { className };
  if (size) props.size = size;
  if (ariaHidden !== undefined) props['aria-hidden'] = ariaHidden;
  return <BsGrid {...props} />;
};

export const BarChartIcon: React.FC<IconProps> = ({ className = 'w-5 h-5', size, 'aria-hidden': ariaHidden = true }) => {
  const props: any = { className };
  if (size) props.size = size;
  if (ariaHidden !== undefined) props['aria-hidden'] = ariaHidden;
  return <BsBarChart {...props} />;
};

export const MoreVerticalIcon: React.FC<IconProps> = ({ className = 'w-5 h-5', size, 'aria-hidden': ariaHidden = true }) => {
  const props: any = { className };
  if (size) props.size = size;
  if (ariaHidden !== undefined) props['aria-hidden'] = ariaHidden;
  return <BsThreeDotsVertical {...props} />;
};

export const PlusIcon: React.FC<IconProps> = ({ className = 'w-5 h-5', size, 'aria-hidden': ariaHidden = true }) => {
  const props: any = { className };
  if (size) props.size = size;
  if (ariaHidden !== undefined) props['aria-hidden'] = ariaHidden;
  return <BsPlus {...props} />;
};

export const UploadIcon: React.FC<IconProps> = ({ className = 'w-5 h-5', size, 'aria-hidden': ariaHidden = true }) => {
  const props: any = { className };
  if (size) props.size = size;
  if (ariaHidden !== undefined) props['aria-hidden'] = ariaHidden;
  return <BsUpload {...props} />;
};

export const XIcon: React.FC<IconProps> = ({ className = 'w-5 h-5', size, 'aria-hidden': ariaHidden = true }) => {
  const props: any = { className };
  if (size) props.size = size;
  if (ariaHidden !== undefined) props['aria-hidden'] = ariaHidden;
  return <BsX {...props} />;
};

export const LoadingIcon: React.FC<IconProps> = ({ className = 'w-5 h-5', size, 'aria-hidden': ariaHidden = true }) => {
  const props: any = { className };
  if (size) props.size = size;
  if (ariaHidden !== undefined) props['aria-hidden'] = ariaHidden;
  return <AiOutlineLoading3Quarters {...props} />;
};

export const LightningIcon: React.FC<IconProps> = ({ className = 'w-5 h-5', size, 'aria-hidden': ariaHidden = true }) => {
  const props: any = { className };
  if (size) props.size = size;
  if (ariaHidden !== undefined) props['aria-hidden'] = ariaHidden;
  return <BsLightning {...props} />;
};
