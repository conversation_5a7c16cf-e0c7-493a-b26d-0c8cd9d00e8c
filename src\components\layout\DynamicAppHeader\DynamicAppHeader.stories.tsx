import type { <PERSON><PERSON>, StoryObj } from '@storybook/react-vite';
import DynamicAppHeader from './DynamicAppHeader';
import { LightningIcon, BellIcon } from '../../icons';

// Sample icons for the story
const AppIcon = () => (
  <LightningIcon className="w-8 h-8" />
);

const UserAvatar = () => (
  <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
    JD
  </div>
);

const ListIcon = () => (
  <svg
    className="w-5 h-5"
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M4 6h16M4 10h16M4 14h16M4 18h16"
    />
  </svg>
);

const GridIcon = () => (
  <svg
    className="w-5 h-5"
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"
    />
  </svg>
);

const BarChartIcon = () => (
  <svg
    className="w-5 h-5"
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
    />
  </svg>
);

const meta: Meta<typeof DynamicAppHeader> = {
  title: 'Layout/DynamicAppHeader',
  component: DynamicAppHeader,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component:
          'A comprehensive, responsive application header component with two-row layout. Features global navigation, contextual controls, search functionality, and mobile-optimized responsive behavior.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    app: {
      description:
        'Application configuration including name, icon, and navigation links',
    },
    user: {
      description: 'User information including name, avatar, and notifications',
    },
    view: {
      description:
        'View-specific configuration including title, actions, search, pagination, and view modes',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Sample data for stories
const sampleAppData = {
  name: 'Sales',
  icon: <AppIcon />,
  navLinks: [
    { label: 'Orders', href: '/orders', isActive: false },
    { label: 'Products', href: '/products', isActive: false },
    { label: 'Quotations', href: '/quotations', isActive: true },
    { label: 'Invoices', href: '/invoices', isActive: false },
    { label: 'Reporting', href: '/reporting', isActive: false },
    { label: 'Configuration', href: '/configuration', isActive: false },
  ],
};

const sampleUserData = {
  name: 'John Doe',
  avatar: <UserAvatar />,
  notifications: [{ count: 3, icon: <BellIcon /> }],
};

const sampleViewData = {
  title: 'Quotations',
  actions: [
    {
      label: 'New',
      onClick: () => console.log('New clicked'),
      isPrimary: true,
    },
    {
      label: 'Upload',
      onClick: () => console.log('Upload clicked'),
      isPrimary: false,
    },
  ],
  search: {
    filters: [
      { id: 'undelivered', label: 'Undelivered' },
      { id: 'complete', label: 'Complete' },
    ],
    onSearch: (query: string) => console.log('Search:', query),
    onRemoveFilter: (id: any) => console.log('Remove filter:', id),
  },
  pagination: {
    currentRange: '1-80 / 150',
    onNext: () => console.log('Next page'),
    onPrev: () => console.log('Previous page'),
  },
  viewModes: [
    { name: 'List', icon: <ListIcon /> },
    { name: 'Kanban', icon: <GridIcon /> },
    { name: 'Graph', icon: <BarChartIcon /> },
  ],
  activeViewMode: 'List',
};

export const Default: Story = {
  args: {
    app: sampleAppData,
    user: sampleUserData,
    view: sampleViewData,
  },
};

export const WithoutFilters: Story = {
  args: {
    app: sampleAppData,
    user: sampleUserData,
    view: {
      ...sampleViewData,
      search: {
        ...sampleViewData.search,
        filters: [],
      },
    },
  },
};

export const DifferentActiveView: Story = {
  args: {
    app: sampleAppData,
    user: sampleUserData,
    view: {
      ...sampleViewData,
      activeViewMode: 'Kanban',
    },
  },
};

export const MultipleNotifications: Story = {
  args: {
    app: sampleAppData,
    user: {
      ...sampleUserData,
      notifications: [
        { count: 5, icon: <BellIcon /> },
        { count: 12, icon: <BellIcon /> },
        { count: 99, icon: <BellIcon /> },
      ],
    },
    view: sampleViewData,
  },
};

export const LongAppName: Story = {
  args: {
    app: {
      ...sampleAppData,
      name: 'Enterprise Resource Planning',
    },
    user: sampleUserData,
    view: {
      ...sampleViewData,
      title: 'Customer Relationship Management',
    },
  },
};
